#!/usr/bin/env python3
"""
Temporal video generator using face tracking data

This module generates videos with smooth camera movements based on
face tracking data collected at regular intervals.
"""

import cv2
import numpy as np
import logging
import subprocess
import os
from typing import List, Tuple, Optional
from pathlib import Path

from ..tracking.temporal_face_tracker import TrackingSequence, TemporalFaceTracker
from ..crop.calculator import CropWindowCalculator
from .advanced_face_analyzer import AdvancedFaceAnalyzer


class TemporalVideoGenerator:
    """
    Generates videos with smooth camera movements based on face tracking data
    """

    def __init__(self, crop_calculator: Optional[CropWindowCalculator] = None):
        self.logger = logging.getLogger(__name__)
        self.crop_calculator = crop_calculator or CropWindowCalculator()
        self.face_analyzer = AdvancedFaceAnalyzer()  # Professional face analysis

    def generate_video_from_tracking(self, input_video_path: str,
                                   tracking_sequence: TrackingSequence,
                                   output_path: str,
                                   target_width: int = 720,
                                   target_height: int = 1280,
                                   smoothing_factor: float = 0.3,
                                   start_time: float = 0.0,
                                   end_time: Optional[float] = None) -> bool:
        """
        Generate video with smooth face tracking

        Args:
            input_video_path: Path to input video
            tracking_sequence: Face tracking data
            output_path: Path for output video
            target_width: Target video width
            target_height: Target video height
            smoothing_factor: Temporal smoothing factor (0.0-1.0)
            start_time: Start time in seconds
            end_time: End time in seconds (None for full video)

        Returns:
            True if successful, False otherwise
        """
        self.logger.info(f"Generating video from tracking data: {input_video_path}")
        self.logger.info(f"Target size: {target_width}x{target_height}")
        self.logger.info(f"Smoothing factor: {smoothing_factor}")

        # Validate and adjust crop dimensions
        source_width = tracking_sequence.frame_width
        source_height = tracking_sequence.frame_height

        # Store original target dimensions for final output
        final_target_width = target_width
        final_target_height = target_height

        # For vertical video (9:16 aspect ratio), calculate crop dimensions that maintain aspect ratio
        if target_width == 720 and target_height == 1280:  # Standard vertical format
            # Calculate the crop dimensions that maintain 9:16 aspect ratio within source bounds
            source_aspect = source_width / source_height
            target_aspect = 9 / 16  # 0.5625

            if source_aspect > target_aspect:
                # Source is wider than 9:16, crop width to fit height
                crop_width = int(source_height * target_aspect)
                crop_height = source_height
                self.logger.info(f"Source is wider than 9:16, cropping width: {crop_width}x{crop_height}")
            else:
                # Source is taller than 9:16, crop height to fit width
                crop_width = source_width
                crop_height = int(source_width / target_aspect)
                self.logger.info(f"Source is taller than 9:16, cropping height: {crop_width}x{crop_height}")

            # Use the calculated crop dimensions for cropping, but keep original for face calculations
            crop_target_width = crop_width
            crop_target_height = crop_height
        else:
            # For other dimensions, ensure they don't exceed source dimensions
            if target_width > source_width:
                self.logger.warning(f"Target width {target_width} exceeds source width {source_width}, adjusting to {source_width}")
                target_width = source_width

            if target_height > source_height:
                self.logger.warning(f"Target height {target_height} exceeds source height {source_height}, adjusting to {source_height}")
                target_height = source_height

            crop_target_width = target_width
            crop_target_height = target_height

        self.logger.info(f"Crop dimensions: {crop_target_width}x{crop_target_height}")
        self.logger.info(f"Final output dimensions: {final_target_width}x{final_target_height}")

        try:
            # Generate smooth crop sequence using final target dimensions for face calculations
            # but crop dimensions for actual cropping
            crop_sequence = self._generate_smooth_crop_sequence(
                tracking_sequence, final_target_width, final_target_height, smoothing_factor,
                crop_target_width, crop_target_height
            )

            if not crop_sequence:
                self.logger.error("No crop sequence generated")
                return False

            # Filter crop sequence by time range
            if end_time is None:
                end_time = tracking_sequence.duration

            filtered_crops = [
                (ts, x, y) for ts, x, y in crop_sequence
                if start_time <= ts <= end_time
            ]

            if not filtered_crops:
                self.logger.error(f"No crop data in time range {start_time}-{end_time}")
                return False

            self.logger.info(f"Using {len(filtered_crops)} crop positions for time range {start_time:.1f}-{end_time:.1f}s")

            # Generate video using FFmpeg with smooth cropping
            return self._generate_video_with_ffmpeg(
                input_video_path, output_path, filtered_crops,
                crop_target_width, crop_target_height, start_time, end_time,
                tracking_sequence.fps, final_target_width, final_target_height
            )

        except Exception as e:
            self.logger.error(f"Error generating video from tracking: {str(e)}")
            return False

    def _generate_video_with_ffmpeg(self, input_path: str, output_path: str,
                                  crop_sequence: List[Tuple[float, int, int]],
                                  target_width: int, target_height: int,
                                  start_time: float, end_time: float,
                                  fps: float, final_width: Optional[int] = None, final_height: Optional[int] = None) -> bool:
        """
        Generate video using FFmpeg with smooth cropping and scaling

        Args:
            input_path: Input video path
            output_path: Output video path
            crop_sequence: List of (timestamp, crop_x, crop_y) tuples
            target_width: Target crop width (before scaling)
            target_height: Target crop height (before scaling)
            start_time: Start time
            end_time: End time
            fps: Video frame rate
            final_width: Final output width (for scaling)
            final_height: Final output height (for scaling)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create crop filter string with keyframes
            crop_filter = self._create_crop_filter(
                crop_sequence, target_width, target_height, start_time, fps
            )

            # Check if we need segment-based processing
            if crop_filter.startswith("MULTI_SEGMENT:"):
                return self._generate_video_with_multi_segments(
                    input_path, output_path, crop_filter,
                    start_time, end_time, final_width, final_height
                )
            elif crop_filter.startswith("SEGMENT_BASED:"):
                return self._generate_video_with_segments(
                    input_path, output_path, crop_filter,
                    start_time, end_time, final_width, final_height
                )

            # Add scaling if final dimensions are different from crop dimensions
            if final_width and final_height and (target_width != final_width or target_height != final_height):
                video_filter = f"{crop_filter},scale={final_width}:{final_height}"
                self.logger.info(f"Applying crop and scale to {final_width}x{final_height}: {video_filter}")
            else:
                video_filter = crop_filter
                self.logger.info(f"Applying crop only: {video_filter}")

            # Build FFmpeg command
            cmd = [
                'ffmpeg', '-y',
                '-i', input_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-filter:v', video_filter,
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-c:a', 'aac',
                '-b:a', '128k',
                output_path
            ]

            self.logger.info(f"Running FFmpeg command: {' '.join(cmd)}")

            # Run FFmpeg
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"Successfully generated video: {output_path}")
                return True
            else:
                self.logger.error(f"FFmpeg failed: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Error running FFmpeg: {str(e)}")
            return False

    def _generate_video_with_segments(self, input_path: str, output_path: str,
                                    segment_info: str, start_time: float, end_time: float,
                                    final_width: Optional[int] = None, final_height: Optional[int] = None) -> bool:
        """
        Generate video using segment-based approach for face movement

        Args:
            input_path: Input video path
            output_path: Output video path
            segment_info: Segment information string
            start_time: Start time
            end_time: End time
            final_width: Final output width
            final_height: Final output height

        Returns:
            True if successful, False otherwise
        """
        try:
            # Parse segment information: SEGMENT_BASED:width:height:pos1:pos2:transition_time
            parts = segment_info.split(":")
            crop_width = int(parts[1])
            crop_height = int(parts[2])
            first_pos = int(parts[3])
            second_pos = int(parts[4])
            transition_time = float(parts[5])

            self.logger.info(f"🎬 Generating video with segments:")
            self.logger.info(f"Segment 1: {start_time:.1f}s-{transition_time:.1f}s, crop X={first_pos}")
            self.logger.info(f"Segment 2: {transition_time:.1f}s-{end_time:.1f}s, crop X={second_pos}")

            # Create temporary files for segments
            import tempfile
            temp_dir = tempfile.mkdtemp()
            segment1_path = os.path.join(temp_dir, "segment1.mp4")
            segment2_path = os.path.join(temp_dir, "segment2.mp4")

            # Generate segment 1
            segment1_duration = transition_time - start_time
            if segment1_duration > 0:
                crop_filter1 = f"crop={crop_width}:{crop_height}:{first_pos}:0"
                if final_width and final_height:
                    video_filter1 = f"{crop_filter1},scale={final_width}:{final_height}"
                else:
                    video_filter1 = crop_filter1

                cmd1 = [
                    'ffmpeg', '-y',
                    '-i', input_path,
                    '-ss', str(start_time),
                    '-t', str(segment1_duration),
                    '-filter:v', video_filter1,
                    '-c:v', 'libx264', '-preset', 'medium', '-crf', '23',
                    '-c:a', 'aac', '-b:a', '128k',
                    segment1_path
                ]

                self.logger.info(f"Generating segment 1: {' '.join(cmd1)}")
                result1 = subprocess.run(cmd1, capture_output=True, text=True)
                if result1.returncode != 0:
                    self.logger.error(f"Segment 1 failed: {result1.stderr}")
                    return False

            # Generate segment 2
            segment2_duration = end_time - transition_time
            if segment2_duration > 0:
                crop_filter2 = f"crop={crop_width}:{crop_height}:{second_pos}:0"
                if final_width and final_height:
                    video_filter2 = f"{crop_filter2},scale={final_width}:{final_height}"
                else:
                    video_filter2 = crop_filter2

                cmd2 = [
                    'ffmpeg', '-y',
                    '-i', input_path,
                    '-ss', str(transition_time),
                    '-t', str(segment2_duration),
                    '-filter:v', video_filter2,
                    '-c:v', 'libx264', '-preset', 'medium', '-crf', '23',
                    '-c:a', 'aac', '-b:a', '128k',
                    segment2_path
                ]

                self.logger.info(f"Generating segment 2: {' '.join(cmd2)}")
                result2 = subprocess.run(cmd2, capture_output=True, text=True)
                if result2.returncode != 0:
                    self.logger.error(f"Segment 2 failed: {result2.stderr}")
                    return False

            # Concatenate segments
            concat_list = os.path.join(temp_dir, "concat_list.txt")
            with open(concat_list, 'w') as f:
                if segment1_duration > 0:
                    f.write(f"file '{segment1_path}'\n")
                if segment2_duration > 0:
                    f.write(f"file '{segment2_path}'\n")

            cmd_concat = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_list,
                '-c', 'copy',
                output_path
            ]

            self.logger.info(f"Concatenating segments: {' '.join(cmd_concat)}")
            result_concat = subprocess.run(cmd_concat, capture_output=True, text=True)

            # Cleanup
            import shutil
            shutil.rmtree(temp_dir)

            if result_concat.returncode == 0:
                self.logger.info(f"✅ Successfully generated segmented video: {output_path}")
                return True
            else:
                self.logger.error(f"Concatenation failed: {result_concat.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Error in segment-based generation: {str(e)}")
            return False

    def _generate_video_with_multi_segments(self, input_path: str, output_path: str,
                                          segment_info: str, start_time: float, end_time: float,
                                          final_width: Optional[int] = None, final_height: Optional[int] = None) -> bool:
        """
        Generate video using intelligent multi-segment approach for multiple face movements

        Args:
            input_path: Input video path
            output_path: Output video path
            segment_info: Multi-segment information string
            start_time: Start time
            end_time: End time
            final_width: Final output width
            final_height: Final output height

        Returns:
            True if successful, False otherwise
        """
        try:
            # Parse multi-segment information: MULTI_SEGMENT:width:height:start1:end1:pos1:start2:end2:pos2:...
            parts = segment_info.split(":")
            crop_width = int(parts[1])
            crop_height = int(parts[2])

            # Parse segments from the remaining parts
            segment_data = parts[3:]
            segments = []
            for i in range(0, len(segment_data), 3):
                if i + 2 < len(segment_data):
                    segments.append({
                        'start_time': float(segment_data[i]),
                        'end_time': float(segment_data[i + 1]),
                        'position': int(segment_data[i + 2])
                    })

            self.logger.info(f"🎬 Generating video with {len(segments)} intelligent segments:")
            for i, seg in enumerate(segments):
                self.logger.info(f"  Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s, crop X={seg['position']}")

            # Create temporary files for segments
            import tempfile
            temp_dir = tempfile.mkdtemp()
            segment_paths = []

            # Generate each segment
            for i, segment in enumerate(segments):
                segment_path = os.path.join(temp_dir, f"segment_{i+1}.mp4")
                segment_duration = segment['end_time'] - segment['start_time']

                if segment_duration > 0:
                    crop_filter = f"crop={crop_width}:{crop_height}:{segment['position']}:0"
                    if final_width and final_height:
                        video_filter = f"{crop_filter},scale={final_width}:{final_height}"
                    else:
                        video_filter = crop_filter

                    cmd = [
                        'ffmpeg', '-y',
                        '-i', input_path,
                        '-ss', str(segment['start_time']),
                        '-t', str(segment_duration),
                        '-filter:v', video_filter,
                        '-c:v', 'libx264', '-preset', 'medium', '-crf', '23',
                        '-c:a', 'aac', '-b:a', '128k',
                        segment_path
                    ]

                    self.logger.info(f"Generating segment {i+1}: {' '.join(cmd)}")
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        self.logger.error(f"Segment {i+1} failed: {result.stderr}")
                        return False

                    segment_paths.append(segment_path)

            # Concatenate all segments
            concat_list = os.path.join(temp_dir, "concat_list.txt")
            with open(concat_list, 'w') as f:
                for segment_path in segment_paths:
                    f.write(f"file '{segment_path}'\n")

            cmd_concat = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_list,
                '-c', 'copy',
                output_path
            ]

            self.logger.info(f"Concatenating {len(segment_paths)} segments: {' '.join(cmd_concat)}")
            result_concat = subprocess.run(cmd_concat, capture_output=True, text=True)

            # CRITICAL: Validate final video duration to ensure minimum duration compliance
            if result_concat.returncode == 0:
                try:
                    import ffmpeg
                    probe = ffmpeg.probe(output_path)
                    actual_duration = float(probe['format'].get('duration', 0))
                    expected_duration = end_time - start_time

                    self.logger.info(f"🔍 DURATION VALIDATION: Expected {expected_duration:.2f}s, got {actual_duration:.2f}s")

                    # Allow small tolerance for encoding precision (±0.1s)
                    duration_diff = abs(actual_duration - expected_duration)
                    if duration_diff > 0.1:
                        self.logger.warning(f"⚠️ DURATION MISMATCH: {duration_diff:.2f}s difference detected")
                        self.logger.warning(f"This may indicate segment gaps or timing issues")
                    else:
                        self.logger.info(f"✅ DURATION VALIDATION PASSED: Video duration is correct")

                except Exception as e:
                    self.logger.warning(f"Could not validate output duration: {e}")

            # Cleanup
            import shutil
            shutil.rmtree(temp_dir)

            if result_concat.returncode == 0:
                self.logger.info(f"✅ Successfully generated multi-segment video: {output_path}")
                return True
            else:
                self.logger.error(f"Multi-segment concatenation failed: {result_concat.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Error in multi-segment video generation: {e}")
            return False

    def _create_crop_filter(self, crop_sequence: List[Tuple[float, int, int]],
                          target_width: int, target_height: int,
                          start_time: float, fps: float) -> str:
        """
        Create FFmpeg crop filter with smooth transitions

        Args:
            crop_sequence: List of (timestamp, crop_x, crop_y) tuples
            target_width: Target width
            target_height: Target height
            start_time: Start time offset
            fps: Video frame rate

        Returns:
            FFmpeg crop filter string
        """
        if len(crop_sequence) <= 1:
            # Single crop position
            if crop_sequence:
                _, crop_x, crop_y = crop_sequence[0]
            else:
                crop_x, crop_y = 0, 0
            return f"crop={target_width}:{target_height}:{crop_x}:{crop_y}"

        # Multiple crop positions - create smooth transitions
        # For now, use simple linear interpolation between keyframes
        # This could be enhanced with more sophisticated interpolation

        # Create expressions for smooth x and y movement
        x_positions = [(ts - start_time, x) for ts, x, _ in crop_sequence]
        y_positions = [(ts - start_time, y) for ts, _, y in crop_sequence]

        self.logger.debug(f"Creating position expressions for {len(x_positions)} positions")
        self.logger.debug(f"X positions: {x_positions}")
        self.logger.debug(f"Y positions: {y_positions}")

        x_expr = self._create_position_expression(x_positions, fps, start_time)
        y_expr = self._create_position_expression(y_positions, fps, start_time)

        self.logger.debug(f"Generated X expression: {x_expr}")
        self.logger.debug(f"Generated Y expression: {y_expr}")

        # Check if we need segment-based processing
        if x_expr.startswith("MULTI_SEGMENT:"):
            # Extract multi-segment information
            segment_data = x_expr[14:]  # Remove "MULTI_SEGMENT:" prefix
            parts = segment_data.split(":")

            # Parse segments: [start_time, end_time, position, start_time, end_time, position, ...]
            segments = []
            for i in range(0, len(parts), 3):
                if i + 2 < len(parts):
                    segments.append({
                        'start_time': float(parts[i]),
                        'end_time': float(parts[i + 1]),
                        'position': int(parts[i + 2])
                    })

            self.logger.info(f"🎬 Creating multi-segment video processing with {len(segments)} segments")
            for i, seg in enumerate(segments):
                self.logger.info(f"  Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s at X={seg['position']}")

            # Return special marker for multi-segment processing
            return f"MULTI_SEGMENT:{target_width}:{target_height}:{segment_data}"

        elif x_expr.startswith("SEGMENT_BASED:"):
            # Legacy 2-segment processing
            parts = x_expr.split(":")
            first_pos = int(parts[1])
            second_pos = int(parts[2])
            transition_time = float(parts[3])

            self.logger.info(f"🎬 Creating legacy 2-segment video processing")
            self.logger.info(f"Segment 1: 0-{transition_time:.1f}s at X={first_pos}")
            self.logger.info(f"Segment 2: {transition_time:.1f}s-end at X={second_pos}")

            # Return special marker for segment processing
            return f"SEGMENT_BASED:{target_width}:{target_height}:{first_pos}:{second_pos}:{transition_time:.1f}"

        return f"crop={target_width}:{target_height}:{x_expr}:{y_expr}"

    def _create_position_expression(self, positions: List[Tuple[float, int]], fps: float, clip_start_time: float = 0.0) -> str:
        """
        Create FFmpeg expression for smooth position transitions

        Args:
            positions: List of (timestamp, position) tuples
            fps: Video frame rate

        Returns:
            FFmpeg expression string
        """
        if len(positions) <= 1:
            return str(positions[0][1] if positions else 0)

        # For 2 positions, use simple linear interpolation
        if len(positions) == 2:
            t1, pos1 = positions[0]
            t2, pos2 = positions[1]
            # Linear interpolation: pos1 + (pos2-pos1) * (t-t1)/(t2-t1)
            expr = f"{pos1}+({pos2}-{pos1})*(t-{t1:.1f})/({t2:.1f}-{t1:.1f})"
            self.logger.debug(f"Using linear interpolation: {expr}")
            return expr

        # For multiple positions, detect if there's significant movement
        min_pos = min(pos for _, pos in positions)
        max_pos = max(pos for _, pos in positions)
        position_range = max_pos - min_pos

        self.logger.debug(f"Position range: {min_pos} to {max_pos}, difference: {position_range}")

        # If there's significant movement (>200 pixels), use intelligent multi-segment approach
        if position_range > 200:
            self.logger.info(f"🎯 SIGNIFICANT FACE MOVEMENT DETECTED: {position_range}px range")
            self.logger.info("Using intelligent multi-segment approach for optimal face tracking")

            # Find all major position jumps (>150 pixels between consecutive points)
            jump_threshold = 150
            segments = []
            current_segment_start = 0

            for i in range(1, len(positions)):
                change = abs(positions[i][1] - positions[i-1][1])
                if change > jump_threshold:
                    # End current segment and start new one
                    segments.append({
                        'start_idx': current_segment_start,
                        'end_idx': i - 1,
                        'start_time': positions[current_segment_start][0],
                        'end_time': positions[i-1][0],
                        'position': positions[current_segment_start][1]
                    })
                    current_segment_start = i
                    self.logger.info(f"Position jump detected at t={positions[i][0]:.1f}s: {change:.1f}px")

            # Add final segment
            segments.append({
                'start_idx': current_segment_start,
                'end_idx': len(positions) - 1,
                'start_time': positions[current_segment_start][0],
                'end_time': positions[-1][0],
                'position': positions[current_segment_start][1]
            })

            # CRITICAL FIX: Ensure segments cover the full duration from clip_start_time to clip_end_time
            # This prevents duration truncation that violates minimum duration requirements
            if segments and positions:
                # Calculate clip boundaries from the first and last positions
                clip_end_time = clip_start_time + (positions[-1][0] - positions[0][0]) if len(positions) > 1 else clip_start_time + 10.0

                # Extend first segment to start at the beginning of the clip
                segments[0]['start_time'] = clip_start_time

                # Extend last segment to end at the end of the clip
                segments[-1]['end_time'] = clip_end_time

                # Fill any gaps between segments to ensure continuous coverage
                for i in range(len(segments) - 1):
                    current_end = segments[i]['end_time']
                    next_start = segments[i + 1]['start_time']

                    if next_start > current_end:
                        # Gap detected - extend current segment to meet next segment
                        gap_duration = next_start - current_end
                        self.logger.info(f"🔧 DURATION FIX: Filling {gap_duration:.1f}s gap between segments {i+1} and {i+2}")
                        segments[i]['end_time'] = next_start

                total_duration = clip_end_time - clip_start_time
                self.logger.info(f"🔧 DURATION VALIDATION: Ensured segments cover full {total_duration:.1f}s duration")

            self.logger.info(f"🎬 Created {len(segments)} intelligent segments:")
            for i, seg in enumerate(segments):
                self.logger.info(f"  Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s at X={seg['position']}")

            # Return multi-segment marker
            segment_data = []
            for seg in segments:
                segment_data.extend([str(seg['start_time']), str(seg['end_time']), str(seg['position'])])

            return f"MULTI_SEGMENT:{':'.join(segment_data)}"

        # For small movements, use average
        avg_position = sum(pos for _, pos in positions) // len(positions)
        self.logger.debug(f"Using average position: {avg_position} from {len(positions)} positions")

        return str(avg_position)

    def _generate_smooth_crop_sequence(self, tracking_sequence: TrackingSequence,
                                     target_width: int, target_height: int,
                                     smoothing_factor: float = 0.3,
                                     crop_width: Optional[int] = None,
                                     crop_height: Optional[int] = None) -> List[Tuple[float, int, int]]:
        """
        Generate smooth crop sequence from tracking data

        Args:
            tracking_sequence: Face tracking data
            target_width: Target width for face calculations (final output width)
            target_height: Target height for face calculations (final output height)
            smoothing_factor: Smoothing factor for temporal consistency (0.0-1.0)
            crop_width: Actual crop width (may be different from target for scaling)
            crop_height: Actual crop height (may be different from target for scaling)

        Returns:
            List of (timestamp, crop_x, crop_y) tuples
        """
        # Use crop dimensions if provided, otherwise use target dimensions
        actual_crop_width = crop_width if crop_width is not None else target_width
        actual_crop_height = crop_height if crop_height is not None else target_height

        self.logger.info(f"Generating smooth crop sequence for {len(tracking_sequence.tracking_data)} tracking points")
        self.logger.info(f"Face calculation dimensions: {target_width}x{target_height}")
        self.logger.info(f"Actual crop dimensions: {actual_crop_width}x{actual_crop_height}")

        crop_sequence = []
        previous_crop_x = None
        previous_crop_y = None

        for face_data in tracking_sequence.tracking_data:
            # Calculate optimal crop position using target dimensions for face positioning
            crop_x, crop_y = self._calculate_optimal_crop_position(
                face_data, target_width, target_height
            )

            # If we're using different crop dimensions, recalculate position for actual crop size
            if crop_width is not None and crop_height is not None:
                # Recalculate crop position directly for the actual crop dimensions
                # This ensures proper face centering without scaling distortion
                if face_data.group_bounds and face_data.group_bounds.face_count >= 2:
                    crop_x, crop_y = self._calculate_group_based_crop(
                        face_data.group_bounds, face_data.frame_width, face_data.frame_height,
                        actual_crop_width, actual_crop_height
                    )
                elif face_data.primary_face:
                    crop_x, crop_y = self._calculate_face_based_crop(
                        face_data.primary_face, face_data.frame_width, face_data.frame_height,
                        actual_crop_width, actual_crop_height
                    )
                else:
                    # Fallback to center crop
                    crop_x = (face_data.frame_width - actual_crop_width) // 2
                    crop_y = (face_data.frame_height - actual_crop_height) // 2

            # Apply ultra-precise temporal smoothing with maximum centering preservation
            if previous_crop_x is not None and previous_crop_y is not None:
                # Ultra-precise smoothing that preserves face centering accuracy
                # Use adaptive smoothing with ultra-precision preservation
                adaptive_smoothing = smoothing_factor * 0.5  # Reduce smoothing by 50% for better precision

                # Apply smoothing with floating-point precision, then round
                smooth_crop_x = previous_crop_x * adaptive_smoothing + crop_x * (1 - adaptive_smoothing)
                smooth_crop_y = previous_crop_y * adaptive_smoothing + crop_y * (1 - adaptive_smoothing)

                crop_x = int(round(smooth_crop_x))
                crop_y = int(round(smooth_crop_y))

            # SIMPLE BOUNDARY CHECKING: Just clamp to frame bounds for now
            max_crop_x = max(0, face_data.frame_width - actual_crop_width)
            max_crop_y = max(0, face_data.frame_height - actual_crop_height)

            original_crop_x = crop_x
            original_crop_y = crop_y

            # Simple clamping to frame boundaries
            crop_x = max(0, min(max_crop_x, crop_x))
            crop_y = max(0, min(max_crop_y, crop_y))

            # Debug logging for boundary adjustments
            if crop_x != original_crop_x or crop_y != original_crop_y:
                self.logger.debug(f"🚧 Boundary clamping: original=({original_crop_x}, {original_crop_y}), "
                                f"clamped=({crop_x}, {crop_y}), max_bounds=({max_crop_x}, {max_crop_y})")

            # Log the face position relative to the crop for debugging the cutting issue
            if face_data.primary_face:
                face = face_data.primary_face
                face_left_in_crop = face.x - crop_x
                face_right_in_crop = face.x + face.width - crop_x
                face_center_in_crop = (face.x + face.width/2) - crop_x
                crop_center = actual_crop_width / 2

                self.logger.debug(f"🔍 Face position analysis: face_left_in_crop={face_left_in_crop}, "
                                f"face_right_in_crop={face_right_in_crop}, face_center_in_crop={face_center_in_crop:.1f}, "
                                f"crop_center={crop_center}, offset={face_center_in_crop - crop_center:.1f}")

                # Check for potential face cutting
                if face_left_in_crop < 0:
                    self.logger.warning(f"⚠️ LEFT SIDE OF FACE MAY BE CUT: face extends {-face_left_in_crop}px left of crop")
                if face_right_in_crop > actual_crop_width:
                    self.logger.warning(f"⚠️ RIGHT SIDE OF FACE MAY BE CUT: face extends {face_right_in_crop - actual_crop_width}px right of crop")

            crop_sequence.append((face_data.timestamp, crop_x, crop_y))

            previous_crop_x = crop_x
            previous_crop_y = crop_y

        self.logger.info(f"Generated {len(crop_sequence)} crop positions")
        return crop_sequence

    def _calculate_optimal_crop_position(self, face_data, target_width: int, target_height: int) -> Tuple[int, int]:
        """
        Calculate optimal crop position for face data using sophisticated crop calculator

        Args:
            face_data: Face detection data
            target_width: Target crop width
            target_height: Target crop height

        Returns:
            (crop_x, crop_y) tuple
        """
        # Use the sophisticated crop calculator for better horizontal centering
        if self.crop_calculator and face_data.primary_face:
            try:
                crop_window = self.crop_calculator.calculate_crop_window(
                    primary_face=face_data.primary_face,
                    frame_width=face_data.frame_width,
                    frame_height=face_data.frame_height,
                    target_width=target_width,
                    target_height=target_height,
                    timestamp=face_data.timestamp,
                    group_bounds=face_data.group_bounds
                )
                return crop_window.x, crop_window.y
            except Exception as e:
                self.logger.warning(f"Crop calculator failed, using fallback: {str(e)}")

        # Fallback to improved centering logic
        if face_data.group_bounds and face_data.group_bounds.face_count >= 2:
            # Use group bounds with better horizontal centering
            crop_x, crop_y = self._calculate_group_based_crop(
                face_data.group_bounds, face_data.frame_width, face_data.frame_height,
                target_width, target_height
            )
        elif face_data.primary_face:
            # Use primary face with better horizontal centering
            crop_x, crop_y = self._calculate_face_based_crop(
                face_data.primary_face, face_data.frame_width, face_data.frame_height,
                target_width, target_height
            )
        else:
            # Fallback to center crop
            crop_x = (face_data.frame_width - target_width) // 2
            crop_y = (face_data.frame_height - target_height) // 2

        # SIMPLE BOUNDARY CHECKING: Just clamp to frame bounds for now
        max_crop_x = max(0, face_data.frame_width - target_width)
        max_crop_y = max(0, face_data.frame_height - target_height)

        original_crop_x = crop_x
        original_crop_y = crop_y

        # Simple clamping to frame boundaries
        crop_x = max(0, min(max_crop_x, crop_x))
        crop_y = max(0, min(max_crop_y, crop_y))

        # Debug logging for boundary adjustments
        if crop_x != original_crop_x or crop_y != original_crop_y:
            self.logger.debug(f"🚧 Boundary clamping: original=({original_crop_x}, {original_crop_y}), "
                            f"clamped=({crop_x}, {crop_y}), max_bounds=({max_crop_x}, {max_crop_y})")

        # Log the face position relative to the crop for debugging the cutting issue
        if hasattr(face_data, 'primary_face') and face_data.primary_face:
            face = face_data.primary_face
            face_left_in_crop = face.x - crop_x
            face_right_in_crop = face.x + face.width - crop_x
            face_center_in_crop = (face.x + face.width/2) - crop_x
            crop_center = target_width / 2

            self.logger.debug(f"🔍 Face position analysis: face_left_in_crop={face_left_in_crop}, "
                            f"face_right_in_crop={face_right_in_crop}, face_center_in_crop={face_center_in_crop:.1f}, "
                            f"crop_center={crop_center}, offset={face_center_in_crop - crop_center:.1f}")

            # Check for potential face cutting
            if face_left_in_crop < 0:
                self.logger.warning(f"⚠️ LEFT SIDE OF FACE MAY BE CUT: face extends {-face_left_in_crop}px left of crop")
            if face_right_in_crop > target_width:
                self.logger.warning(f"⚠️ RIGHT SIDE OF FACE MAY BE CUT: face extends {face_right_in_crop - target_width}px right of crop")

        return crop_x, crop_y

    def _calculate_group_based_crop(self, group_bounds, frame_width: int, frame_height: int,
                                  target_width: int, target_height: int) -> Tuple[int, int]:
        """PROFESSIONAL GROUP CENTERING - Advanced multi-face composition"""
        if target_width < target_height:  # Vertical format
            # === ULTRA-PRECISE HORIZONTAL CENTERING ===
            # World-class horizontal centering for group with maximum precision
            crop_x = int(round(group_bounds.center_x - target_width / 2.0))

            # === PROFESSIONAL GROUP POSITIONING ===
            # Advanced composition for multiple faces using:
            # 1. Group dynamics analysis
            # 2. Social media optimization
            # 3. Breathing room calculation
            # 4. Visual balance principles

            # Calculate group metrics for intelligent positioning
            group_area_ratio = (group_bounds.width * group_bounds.height) / (frame_width * frame_height)
            group_height_ratio = group_bounds.height / target_height if target_height > 0 else 0.1
            group_width_ratio = group_bounds.width / target_width if target_width > 0 else 0.1

            # Estimate average eye level for the group (30% down from group top)
            group_eye_level_y = group_bounds.y + (group_bounds.height * 0.3)

            # Advanced group composition rules
            if group_area_ratio > 0.25:  # LARGE GROUP (>25% of frame)
                # Position higher to accommodate all faces
                desired_eye_y = int(target_height * 0.25)  # 25% from top
                self.logger.debug(f"Large group detected (area: {group_area_ratio:.3f}), positioned high")

            elif group_area_ratio > 0.15:  # MEDIUM-LARGE GROUP (15-25% of frame)
                # Balanced positioning for medium groups
                desired_eye_y = int(target_height * 0.28)  # 28% from top
                self.logger.debug(f"Medium-large group detected (area: {group_area_ratio:.3f}), balanced position")

            elif group_area_ratio > 0.08:  # MEDIUM GROUP (8-15% of frame)
                # Standard social media positioning
                desired_eye_y = int(target_height * 0.30)  # 30% from top
                self.logger.debug(f"Medium group detected (area: {group_area_ratio:.3f}), social media optimized")

            elif group_area_ratio > 0.04:  # SMALL GROUP (4-8% of frame)
                # Position for better visibility
                desired_eye_y = int(target_height * 0.32)  # 32% from top
                self.logger.debug(f"Small group detected (area: {group_area_ratio:.3f}), enhanced visibility")

            else:  # DISTANT GROUP (<4% of frame)
                # Higher positioning for very small groups
                desired_eye_y = int(target_height * 0.28)  # 28% from top
                self.logger.debug(f"Distant group detected (area: {group_area_ratio:.3f}), maximized visibility")

            # Calculate crop position based on group eye level
            crop_y = int(group_eye_level_y - desired_eye_y)

            # === ADVANCED GROUP REFINEMENTS ===

            # 1. Group spread compensation
            if group_width_ratio > 0.9:  # Very wide group
                crop_y += int(target_height * 0.02)  # Slightly lower for wide groups
                self.logger.debug("Applied wide group compensation")
            elif group_width_ratio < 0.4:  # Narrow group
                crop_y -= int(target_height * 0.01)  # Slightly higher for narrow groups
                self.logger.debug("Applied narrow group compensation")

            # 2. Group headroom optimization
            # Ensure adequate space above the group
            min_group_headroom = int(target_height * 0.06)  # Minimum 6% headroom for groups
            group_top_in_crop = group_bounds.y - crop_y
            if group_top_in_crop < min_group_headroom:
                crop_y = group_bounds.y - min_group_headroom
                self.logger.debug("Applied minimum group headroom correction")

            # 3. Group breathing room at bottom
            # Ensure faces don't get cut off at bottom
            min_bottom_space = int(target_height * 0.15)  # 15% space below group
            group_bottom_in_crop = (group_bounds.y + group_bounds.height) - crop_y
            max_allowed_group_bottom = target_height - min_bottom_space
            if group_bottom_in_crop > max_allowed_group_bottom:
                crop_y += group_bottom_in_crop - max_allowed_group_bottom
                self.logger.debug("Applied group bottom breathing room correction")

            self.logger.debug(f"Group positioning: area_ratio={group_area_ratio:.3f}, "
                            f"faces={group_bounds.face_count}, eye_y={desired_eye_y}, crop_y={crop_y}")

        else:  # Square or landscape format
            # Standard centering for non-vertical formats
            crop_x = group_bounds.center_x - target_width // 2
            crop_y = group_bounds.center_y - target_height // 2

        return crop_x, crop_y

    def _calculate_face_based_crop(self, face, frame_width: int, frame_height: int,
                                 target_width: int, target_height: int) -> Tuple[int, int]:
        """SIMPLIFIED FACE CENTERING - Focus on the core issue"""

        # Simple horizontal centering - center the face horizontally in the crop
        face_center_x = face.x + face.width / 2.0
        crop_x = int(face_center_x - target_width / 2.0)

        # Simple vertical centering - center the face vertically in the crop
        face_center_y = face.y + face.height / 2.0
        crop_y = int(face_center_y - target_height / 2.0)

        # Debug logging for center calculation
        self.logger.debug(f"🎯 Simple face centering: face_center=({face_center_x:.1f}, {face_center_y:.1f}), "
                        f"crop=({crop_x}, {crop_y}), target_size=({target_width}, {target_height})")

        return crop_x, crop_y

    def generate_preview_frames(self, input_video_path: str,
                              tracking_sequence: TrackingSequence,
                              output_dir: str,
                              target_width: int = 720,
                              target_height: int = 1280,
                              frame_interval: float = 5.0) -> List[str]:
        """
        Generate preview frames showing the tracking results

        Args:
            input_video_path: Path to input video
            tracking_sequence: Face tracking data
            output_dir: Directory for preview frames
            target_width: Target crop width
            target_height: Target crop height
            frame_interval: Interval between preview frames in seconds

        Returns:
            List of generated frame paths
        """
        self.logger.info(f"Generating preview frames for tracking visualization")

        os.makedirs(output_dir, exist_ok=True)

        cap = cv2.VideoCapture(input_video_path)
        if not cap.isOpened():
            self.logger.error(f"Could not open video: {input_video_path}")
            return []

        try:
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_paths = []

            # Generate preview at specified intervals
            current_time = 0.0
            frame_count = 0

            while current_time < tracking_sequence.duration:
                # Find closest tracking data
                closest_data = min(
                    tracking_sequence.tracking_data,
                    key=lambda x: abs(x.timestamp - current_time)
                )

                # Seek to frame
                frame_number = int(current_time * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                ret, frame = cap.read()

                if ret and frame is not None:
                    # Draw face detection results
                    preview_frame = self._draw_tracking_overlay(
                        frame, closest_data, target_width, target_height
                    )

                    # Save frame
                    frame_path = os.path.join(output_dir, f"preview_{frame_count:04d}.jpg")
                    cv2.imwrite(frame_path, preview_frame)
                    frame_paths.append(frame_path)

                    self.logger.debug(f"Generated preview frame: {frame_path}")

                current_time += frame_interval
                frame_count += 1

            self.logger.info(f"Generated {len(frame_paths)} preview frames")
            return frame_paths

        finally:
            cap.release()

    def _draw_tracking_overlay(self, frame: np.ndarray, face_data,
                             target_width: int, target_height: int) -> np.ndarray:
        """
        Draw tracking overlay on frame

        Args:
            frame: Input frame
            face_data: Face detection data
            target_width: Target crop width
            target_height: Target crop height

        Returns:
            Frame with overlay
        """
        overlay_frame = frame.copy()

        # Draw detected faces
        for face in face_data.faces:
            cv2.rectangle(overlay_frame,
                         (face.x, face.y),
                         (face.x + face.width, face.y + face.height),
                         (0, 255, 0), 2)
            cv2.putText(overlay_frame, f"{face.confidence:.2f}",
                       (face.x, face.y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # Draw primary face
        if face_data.primary_face:
            face = face_data.primary_face
            cv2.rectangle(overlay_frame,
                         (face.x, face.y),
                         (face.x + face.width, face.y + face.height),
                         (0, 0, 255), 3)

        # Draw group bounds
        if face_data.group_bounds:
            gb = face_data.group_bounds
            cv2.rectangle(overlay_frame,
                         (gb.x, gb.y),
                         (gb.x + gb.width, gb.y + gb.height),
                         (255, 0, 0), 2)

        # Draw crop window
        crop_x, crop_y = self._calculate_optimal_crop_position(
            face_data, target_width, target_height
        )
        cv2.rectangle(overlay_frame,
                     (crop_x, crop_y),
                     (crop_x + target_width, crop_y + target_height),
                     (255, 255, 0), 2)

        # Add timestamp
        cv2.putText(overlay_frame, f"t={face_data.timestamp:.1f}s",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        return overlay_frame
